#!/usr/bin/env python3
"""
Test script for the DevOps Multi-Agent System workflow
"""
import requests
import json
import time

API_BASE = "http://localhost:8000/api/v1"

def test_agent_status():
    """Test agent status endpoint"""
    print("Testing agent status...")
    response = requests.get(f"{API_BASE}/agents/status")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    return response.status_code == 200

def test_requirement_submission():
    """Test requirement submission and complete workflow"""
    print("\nTesting requirement submission...")
    
    requirement = {
        "title": "AI-Powered Image Classification System",
        "description": "Develop an AI system for real-time image classification with high accuracy",
        "requirement_type": "computer_vision",
        "technical_specs": {
            "image_processing": "real-time",
            "accuracy": "95%+",
            "framework": "pytorch",
            "deployment": "docker"
        },
        "expected_features": [
            "Image upload API",
            "Real-time classification",
            "Model training interface",
            "Results dashboard",
            "Performance monitoring"
        ],
        "performance_requirements": {
            "latency": "< 100ms",
            "throughput": "1000 requests/min",
            "accuracy": "95%"
        },
        "deadline": "2024-03-01",
        "priority": "high"
    }
    
    print(f"Submitting requirement: {requirement['title']}")
    response = requests.post(
        f"{API_BASE}/agents/workflow/complete",
        json=requirement,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result['success']}")
        print(f"Message: {result['message']}")
        
        if result['success']:
            data = result['data']
            
            # Print analysis results
            print("\n=== ANALYSIS RESULTS ===")
            analysis = data['analysis']
            print(f"Selected Framework: {analysis['selected_framework']}")
            print(f"Project Name: {analysis['project_config']['project_name']}")
            print(f"Complexity: {analysis['requirement_analysis']['complexity']}")
            print(f"Estimated Duration: {analysis['requirement_analysis']['estimated_duration']}")
            print(f"Team Size: {analysis['requirement_analysis']['recommended_team_size']}")
            
            # Print initialization results
            print("\n=== INITIALIZATION RESULTS ===")
            init = data['initialization']
            print(f"Project Path: {init['project_path']}")
            print(f"Git Initialized: {init['git_initialized']}")
            print(f"Structure Created: {init['structure_created']}")
            print(f"Configs Created: {init['configs_created']}")
            print(f"Environment Initialized: {init['environment_initialized']}")
            
            # Print decomposition results
            print("\n=== FEATURE DECOMPOSITION ===")
            decomp = data['decomposition']
            print(f"Total Modules: {decomp['development_plan']['total_modules']}")
            print(f"Development Phases: {len(decomp['development_plan']['development_phases'])}")
            
            print("\nFeature Modules:")
            for module in decomp['feature_modules']:
                print(f"  - {module['name']}: {module['description']} ({module['priority']} priority)")
            
            print("\nBranch Creation Status:")
            for branch, status in decomp['branches_created'].items():
                print(f"  - feature/{branch}: {'✓' if status else '✗'}")
            
            print("\nDevelopment Plan:")
            for phase in decomp['development_plan']['development_phases']:
                print(f"  Phase {phase['phase']}: {phase['name']} ({phase['estimated_duration']})")
                for module in phase['modules']:
                    print(f"    - {module['name']}")
            
            return True
    else:
        print(f"Error: {response.text}")
        return False

def test_frameworks():
    """Test frameworks endpoint"""
    print("\nTesting frameworks endpoint...")
    response = requests.get(f"{API_BASE}/agents/frameworks")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        frameworks = response.json()['frameworks']
        print(f"Available frameworks: {len(frameworks)}")
        for fw in frameworks:
            print(f"  - {fw['name']}: {fw['description']}")
        return True
    return False

def main():
    """Run all tests"""
    print("=== DevOps Multi-Agent System Test ===\n")
    
    tests = [
        test_agent_status,
        test_frameworks,
        test_requirement_submission
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED\n")
            else:
                print("✗ FAILED\n")
        except Exception as e:
            print(f"✗ ERROR: {e}\n")
    
    print(f"=== Test Results: {passed}/{len(tests)} passed ===")

if __name__ == "__main__":
    main()
