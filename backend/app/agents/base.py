"""
Base Agent Class for Multi-Agent System
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from enum import Enum
from pydantic import BaseModel
import uuid
from datetime import datetime


class AgentStatus(str, Enum):
    """Agent status enumeration"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentMessage(BaseModel):
    """Message structure for agent communication"""
    id: str = None
    sender_id: str
    receiver_id: str
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime = None
    
    def __init__(self, **data):
        if data.get('id') is None:
            data['id'] = str(uuid.uuid4())
        if data.get('timestamp') is None:
            data['timestamp'] = datetime.utcnow()
        super().__init__(**data)


class AgentTask(BaseModel):
    """Task structure for agent processing"""
    id: str = None
    agent_id: str
    task_type: str
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    updated_at: datetime = None
    error_message: Optional[str] = None
    
    def __init__(self, **data):
        if data.get('id') is None:
            data['id'] = str(uuid.uuid4())
        if data.get('created_at') is None:
            data['created_at'] = datetime.utcnow()
        if data.get('updated_at') is None:
            data['updated_at'] = datetime.utcnow()
        super().__init__(**data)


class BaseAgent(ABC):
    """Base class for all agents in the multi-agent system"""
    
    def __init__(self, agent_id: str, name: str, description: str = ""):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.status = AgentStatus.IDLE
        self.capabilities: List[str] = []
        self.current_tasks: List[AgentTask] = []
        self.message_queue: List[AgentMessage] = []
        
    @abstractmethod
    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """Process a task assigned to this agent"""
        pass
    
    @abstractmethod
    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle incoming messages from other agents"""
        pass
    
    def get_capabilities(self) -> List[str]:
        """Return list of agent capabilities"""
        return self.capabilities
    
    def get_status(self) -> AgentStatus:
        """Return current agent status"""
        return self.status
    
    def add_task(self, task: AgentTask) -> bool:
        """Add a new task to the agent's queue"""
        if self.status == AgentStatus.OFFLINE:
            return False
        
        self.current_tasks.append(task)
        if self.status == AgentStatus.IDLE:
            self.status = AgentStatus.BUSY
        return True
    
    def complete_task(self, task_id: str, output_data: Dict[str, Any]) -> bool:
        """Mark a task as completed"""
        for task in self.current_tasks:
            if task.id == task_id:
                task.status = TaskStatus.COMPLETED
                task.output_data = output_data
                task.updated_at = datetime.utcnow()
                self.current_tasks.remove(task)
                
                if not self.current_tasks:
                    self.status = AgentStatus.IDLE
                return True
        return False
    
    def fail_task(self, task_id: str, error_message: str) -> bool:
        """Mark a task as failed"""
        for task in self.current_tasks:
            if task.id == task_id:
                task.status = TaskStatus.FAILED
                task.error_message = error_message
                task.updated_at = datetime.utcnow()
                self.current_tasks.remove(task)
                
                if not self.current_tasks:
                    self.status = AgentStatus.IDLE
                return True
        return False
    
    def send_message(self, receiver_id: str, message_type: str, content: Dict[str, Any]) -> AgentMessage:
        """Send a message to another agent"""
        message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content
        )
        return message
    
    def receive_message(self, message: AgentMessage):
        """Receive a message from another agent"""
        self.message_queue.append(message)
    
    async def process_messages(self):
        """Process all messages in the queue"""
        while self.message_queue:
            message = self.message_queue.pop(0)
            response = await self.handle_message(message)
            if response:
                # Send response back (would be handled by message broker)
                pass
