"""
Project Initializer Agent
Responsible for receiving external algorithm requirements and initializing projects
"""

import os
import subprocess
import json
from typing import Dict, Any, List, Optional
from enum import Enum
from pydantic import BaseModel

from .base import BaseAgent, AgentTask, AgentMessage, TaskStatus


class FrameworkType(str, Enum):
    """Available framework types"""

    ALGORITHM_SDK = "algorithm_sdk"
    IMAGE_ANALYSIS = "image_analysis"
    VIDEO_STREAM = "video_stream"


class RequirementType(str, Enum):
    """Requirement types"""

    COMPUTER_VISION = "computer_vision"
    MACHINE_LEARNING = "machine_learning"
    DATA_PROCESSING = "data_processing"
    REAL_TIME_ANALYSIS = "real_time_analysis"


class ProjectRequirement(BaseModel):
    """Project requirement structure"""

    title: str
    description: str
    requirement_type: RequirementType
    technical_specs: Dict[str, Any]
    expected_features: List[str]
    performance_requirements: Dict[str, Any]
    deadline: Optional[str] = None
    priority: str = "medium"  # low, medium, high, urgent


class FrameworkTemplate(BaseModel):
    """Framework template configuration"""

    name: str
    framework_type: FrameworkType
    description: str
    base_dependencies: List[str]
    directory_structure: Dict[str, Any]
    config_templates: Dict[str, str]
    supported_requirements: List[RequirementType]


class ProjectInitializerAgent(BaseAgent):
    """Agent responsible for project initialization based on requirements"""

    def __init__(self):
        super().__init__(
            agent_id="project_initializer",
            name="Project Initializer Agent",
            description="Analyzes requirements and initializes projects with appropriate frameworks",
        )
        self.capabilities = [
            "requirement_analysis",
            "framework_selection",
            "project_initialization",
            "repository_creation",
            "branch_management",
            "feature_decomposition",
        ]

        # Initialize framework templates
        self.framework_templates = self._load_framework_templates()

    def _load_framework_templates(self) -> Dict[FrameworkType, FrameworkTemplate]:
        """Load available framework templates"""
        return {
            FrameworkType.ALGORITHM_SDK: FrameworkTemplate(
                name="Algorithm SDK Framework",
                framework_type=FrameworkType.ALGORITHM_SDK,
                description="General-purpose algorithm development framework",
                base_dependencies=[
                    "numpy",
                    "scipy",
                    "scikit-learn",
                    "pandas",
                    "matplotlib",
                    "jupyter",
                    "pytest",
                ],
                directory_structure={
                    "src": {"algorithms": {}, "utils": {}, "models": {}},
                    "tests": {"unit": {}, "integration": {}},
                    "docs": {},
                    "examples": {},
                    "data": {"raw": {}, "processed": {}},
                    "configs": {},
                },
                config_templates={
                    "requirements.txt": "numpy>=1.21.0\nscipy>=1.7.0\nscikit-learn>=1.0.0",
                    "setup.py": "# Algorithm SDK setup configuration",
                    ".gitignore": "*.pyc\n__pycache__/\n.pytest_cache/\ndata/raw/*",
                },
                supported_requirements=[
                    RequirementType.MACHINE_LEARNING,
                    RequirementType.DATA_PROCESSING,
                ],
            ),
            FrameworkType.IMAGE_ANALYSIS: FrameworkTemplate(
                name="Image Analysis Service",
                framework_type=FrameworkType.IMAGE_ANALYSIS,
                description="Image processing and computer vision service framework",
                base_dependencies=[
                    "opencv-python",
                    "pillow",
                    "numpy",
                    "torch",
                    "torchvision",
                    "fastapi",
                    "uvicorn",
                    "python-multipart",
                ],
                directory_structure={
                    "src": {
                        "image_processing": {},
                        "models": {
                            "detection": {},
                            "classification": {},
                            "segmentation": {},
                        },
                        "api": {},
                        "utils": {},
                    },
                    "tests": {},
                    "models": {"pretrained": {}},
                    "data": {"samples": {}},
                    "configs": {},
                },
                config_templates={
                    "requirements.txt": "opencv-python>=4.5.0\nPillow>=8.0.0\ntorch>=1.9.0",
                    "docker-compose.yml": "# Image analysis service docker configuration",
                },
                supported_requirements=[
                    RequirementType.COMPUTER_VISION,
                    RequirementType.MACHINE_LEARNING,
                ],
            ),
            FrameworkType.VIDEO_STREAM: FrameworkTemplate(
                name="Video Stream Analysis Service",
                framework_type=FrameworkType.VIDEO_STREAM,
                description="Real-time video stream processing and analysis framework",
                base_dependencies=[
                    "opencv-python",
                    "ffmpeg-python",
                    "numpy",
                    "torch",
                    "fastapi",
                    "websockets",
                    "redis",
                    "celery",
                ],
                directory_structure={
                    "src": {
                        "stream_processing": {},
                        "analysis": {"real_time": {}, "batch": {}},
                        "api": {"websocket": {}, "rest": {}},
                        "storage": {},
                        "utils": {},
                    },
                    "tests": {},
                    "configs": {"stream": {}, "analysis": {}},
                    "data": {"streams": {}, "results": {}},
                },
                config_templates={
                    "requirements.txt": "opencv-python>=4.5.0\nffmpeg-python>=0.2.0\nredis>=3.5.0",
                    "docker-compose.yml": "# Video stream analysis service configuration",
                },
                supported_requirements=[
                    RequirementType.REAL_TIME_ANALYSIS,
                    RequirementType.COMPUTER_VISION,
                ],
            ),
        }

    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """Process project initialization task"""
        try:
            task_type = task.task_type

            if task_type == "analyze_requirement":
                return await self._analyze_requirement(task.input_data)
            elif task_type == "initialize_project":
                return await self._initialize_project(task.input_data)
            elif task_type == "decompose_features":
                return await self._decompose_features(task.input_data)
            else:
                raise ValueError(f"Unknown task type: {task_type}")

        except Exception as e:
            return {"error": str(e), "status": "failed"}

    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle messages from other agents"""
        message_type = message.message_type
        content = message.content

        if message_type == "requirement_submission":
            # Process new requirement submission
            task = AgentTask(
                agent_id=self.agent_id,
                task_type="analyze_requirement",
                input_data=content,
            )
            result = await self.process_task(task)

            return self.send_message(
                receiver_id=message.sender_id,
                message_type="requirement_analysis_result",
                content=result,
            )

        return None

    async def _analyze_requirement(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project requirement and select appropriate framework"""
        try:
            requirement = ProjectRequirement(**input_data["requirement"])

            # Select best framework based on requirement type
            selected_framework = self._select_framework(requirement)

            # Generate project configuration
            project_config = self._generate_project_config(
                requirement, selected_framework
            )

            return {
                "status": "success",
                "selected_framework": selected_framework.framework_type.value,
                "framework_details": selected_framework.dict(),
                "project_config": project_config,
                "requirement_analysis": {
                    "complexity": self._assess_complexity(requirement),
                    "estimated_duration": self._estimate_duration(requirement),
                    "recommended_team_size": self._recommend_team_size(requirement),
                },
            }

        except Exception as e:
            return {"status": "error", "message": str(e)}

    def _select_framework(self, requirement: ProjectRequirement) -> FrameworkTemplate:
        """Select the most appropriate framework for the requirement"""
        # Score each framework based on requirement compatibility
        framework_scores = {}

        for framework_type, template in self.framework_templates.items():
            score = 0

            # Check requirement type compatibility
            if requirement.requirement_type in template.supported_requirements:
                score += 10

            # Check technical specifications alignment
            tech_specs = requirement.technical_specs
            if framework_type == FrameworkType.ALGORITHM_SDK:
                if "machine_learning" in tech_specs or "data_processing" in tech_specs:
                    score += 5
            elif framework_type == FrameworkType.IMAGE_ANALYSIS:
                if "image_processing" in tech_specs or "computer_vision" in tech_specs:
                    score += 5
            elif framework_type == FrameworkType.VIDEO_STREAM:
                if "real_time" in tech_specs or "video_processing" in tech_specs:
                    score += 5

            framework_scores[framework_type] = score

        # Select framework with highest score
        best_framework = max(framework_scores, key=framework_scores.get)
        return self.framework_templates[best_framework]

    def _generate_project_config(
        self, requirement: ProjectRequirement, framework: FrameworkTemplate
    ) -> Dict[str, Any]:
        """Generate project configuration based on requirement and framework"""
        return {
            "project_name": requirement.title.lower().replace(" ", "_"),
            "framework_type": framework.framework_type.value,
            "dependencies": framework.base_dependencies,
            "directory_structure": framework.directory_structure,
            "config_files": framework.config_templates,
            "git_config": {
                "main_branch": "main",
                "develop_branch": "develop",
                "feature_prefix": "feature/",
                "release_prefix": "release/",
                "hotfix_prefix": "hotfix/",
            },
        }

    def _assess_complexity(self, requirement: ProjectRequirement) -> str:
        """Assess project complexity based on requirements"""
        feature_count = len(requirement.expected_features)
        tech_specs_count = len(requirement.technical_specs)

        if feature_count <= 3 and tech_specs_count <= 2:
            return "low"
        elif feature_count <= 7 and tech_specs_count <= 5:
            return "medium"
        else:
            return "high"

    def _estimate_duration(self, requirement: ProjectRequirement) -> str:
        """Estimate project duration"""
        complexity = self._assess_complexity(requirement)

        duration_map = {
            "low": "2-4 weeks",
            "medium": "1-2 months",
            "high": "3-6 months",
        }

        return duration_map.get(complexity, "unknown")

    def _recommend_team_size(self, requirement: ProjectRequirement) -> int:
        """Recommend team size based on complexity"""
        complexity = self._assess_complexity(requirement)

        size_map = {"low": 2, "medium": 3, "high": 5}

        return size_map.get(complexity, 3)

    async def _initialize_project(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize project repository and environment"""
        try:
            project_config = input_data["project_config"]
            project_name = project_config["project_name"]
            framework_type = project_config["framework_type"]

            # Create project directory
            project_path = f"/tmp/projects/{project_name}"
            os.makedirs(project_path, exist_ok=True)

            # Initialize git repository
            git_result = await self._initialize_git_repo(
                project_path, project_config["git_config"]
            )

            # Create directory structure
            structure_result = await self._create_directory_structure(
                project_path, project_config["directory_structure"]
            )

            # Create configuration files
            config_result = await self._create_config_files(
                project_path, project_config["config_files"]
            )

            # Initialize virtual environment (for Python projects)
            env_result = await self._initialize_environment(
                project_path, project_config["dependencies"]
            )

            return {
                "status": "success",
                "project_path": project_path,
                "git_initialized": git_result,
                "structure_created": structure_result,
                "configs_created": config_result,
                "environment_initialized": env_result,
            }

        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def _initialize_git_repo(
        self, project_path: str, git_config: Dict[str, Any]
    ) -> bool:
        """Initialize git repository with gitflow"""
        try:
            # Initialize git repo
            subprocess.run(["git", "init"], cwd=project_path, check=True)

            # Create initial commit
            subprocess.run(["git", "add", "."], cwd=project_path, check=True)
            subprocess.run(
                ["git", "commit", "-m", "Initial commit"], cwd=project_path, check=True
            )

            # Initialize gitflow
            subprocess.run(["git", "flow", "init", "-d"], cwd=project_path, check=True)

            return True
        except subprocess.CalledProcessError:
            return False

    async def _create_directory_structure(
        self, project_path: str, structure: Dict[str, Any]
    ) -> bool:
        """Create project directory structure"""
        try:

            def create_dirs(base_path: str, struct: Dict[str, Any]):
                for name, content in struct.items():
                    dir_path = os.path.join(base_path, name)
                    os.makedirs(dir_path, exist_ok=True)

                    # Create __init__.py for Python packages
                    if name in ["src", "tests"] or any(
                        key in name for key in ["api", "models", "utils"]
                    ):
                        init_file = os.path.join(dir_path, "__init__.py")
                        with open(init_file, "w") as f:
                            f.write(f"# {name} package\n")

                    if isinstance(content, dict) and content:
                        create_dirs(dir_path, content)

            create_dirs(project_path, structure)
            return True
        except Exception:
            return False

    async def _create_config_files(
        self, project_path: str, config_files: Dict[str, str]
    ) -> bool:
        """Create configuration files"""
        try:
            for filename, content in config_files.items():
                file_path = os.path.join(project_path, filename)
                with open(file_path, "w") as f:
                    f.write(content)
            return True
        except Exception:
            return False

    async def _initialize_environment(
        self, project_path: str, dependencies: List[str]
    ) -> bool:
        """Initialize Python virtual environment"""
        try:
            # Create virtual environment
            subprocess.run(
                ["python", "-m", "venv", "venv"], cwd=project_path, check=True
            )

            # Install dependencies
            pip_path = os.path.join(project_path, "venv", "bin", "pip")
            for dep in dependencies:
                subprocess.run([pip_path, "install", dep], check=True)

            return True
        except subprocess.CalledProcessError:
            return False

    async def _decompose_features(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Decompose requirements into feature modules and create feature branches"""
        try:
            requirement = ProjectRequirement(**input_data["requirement"])
            project_path = input_data.get(
                "project_path",
                f"/tmp/projects/{requirement.title.lower().replace(' ', '_')}",
            )

            # Analyze features and create modules
            feature_modules = self._analyze_and_create_modules(requirement)

            # Create feature branches for each module
            branch_results = await self._create_feature_branches(
                project_path, feature_modules
            )

            # Generate development plan
            dev_plan = self._generate_development_plan(feature_modules, requirement)

            return {
                "status": "success",
                "feature_modules": feature_modules,
                "branches_created": branch_results,
                "development_plan": dev_plan,
            }

        except Exception as e:
            return {"status": "error", "message": str(e)}

    def _analyze_and_create_modules(
        self, requirement: ProjectRequirement
    ) -> List[Dict[str, Any]]:
        """Analyze requirements and create feature modules"""
        modules = []

        # Base modules for all projects
        base_modules = [
            {
                "name": "core",
                "description": "Core functionality and base classes",
                "priority": "high",
                "estimated_effort": "3-5 days",
                "dependencies": [],
                "files": ["__init__.py", "base.py", "config.py"],
            },
            {
                "name": "utils",
                "description": "Utility functions and helpers",
                "priority": "medium",
                "estimated_effort": "2-3 days",
                "dependencies": ["core"],
                "files": ["__init__.py", "helpers.py", "validators.py"],
            },
        ]

        modules.extend(base_modules)

        # Feature-specific modules based on expected features
        for feature in requirement.expected_features:
            feature_lower = feature.lower()

            if "api" in feature_lower or "interface" in feature_lower:
                modules.append(
                    {
                        "name": "api",
                        "description": f"API implementation for {feature}",
                        "priority": "high",
                        "estimated_effort": "5-7 days",
                        "dependencies": ["core"],
                        "files": [
                            "__init__.py",
                            "endpoints.py",
                            "schemas.py",
                            "middleware.py",
                        ],
                    }
                )

            elif "data" in feature_lower or "processing" in feature_lower:
                modules.append(
                    {
                        "name": "data_processing",
                        "description": f"Data processing for {feature}",
                        "priority": "high",
                        "estimated_effort": "4-6 days",
                        "dependencies": ["core", "utils"],
                        "files": [
                            "__init__.py",
                            "processors.py",
                            "validators.py",
                            "transformers.py",
                        ],
                    }
                )

            elif "model" in feature_lower or "algorithm" in feature_lower:
                modules.append(
                    {
                        "name": "models",
                        "description": f"Model implementation for {feature}",
                        "priority": "high",
                        "estimated_effort": "7-10 days",
                        "dependencies": ["core", "data_processing"],
                        "files": [
                            "__init__.py",
                            "base_model.py",
                            "training.py",
                            "inference.py",
                        ],
                    }
                )

            elif "ui" in feature_lower or "interface" in feature_lower:
                modules.append(
                    {
                        "name": "frontend",
                        "description": f"User interface for {feature}",
                        "priority": "medium",
                        "estimated_effort": "5-8 days",
                        "dependencies": ["api"],
                        "files": ["index.html", "main.js", "styles.css", "components/"],
                    }
                )

            else:
                # Generic feature module
                module_name = feature_lower.replace(" ", "_").replace("-", "_")
                modules.append(
                    {
                        "name": module_name,
                        "description": f"Implementation for {feature}",
                        "priority": "medium",
                        "estimated_effort": "3-5 days",
                        "dependencies": ["core"],
                        "files": ["__init__.py", f"{module_name}.py", "tests.py"],
                    }
                )

        # Add testing module
        modules.append(
            {
                "name": "testing",
                "description": "Test suite and testing utilities",
                "priority": "medium",
                "estimated_effort": "2-4 days",
                "dependencies": ["core"],
                "files": [
                    "__init__.py",
                    "test_core.py",
                    "test_utils.py",
                    "fixtures.py",
                ],
            }
        )

        return modules

    async def _create_feature_branches(
        self, project_path: str, feature_modules: List[Dict[str, Any]]
    ) -> Dict[str, bool]:
        """Create feature branches for each module"""
        branch_results = {}

        try:
            for module in feature_modules:
                module_name = module["name"]
                branch_name = f"feature/{module_name}"

                try:
                    # Create and checkout feature branch
                    subprocess.run(
                        ["git", "checkout", "-b", branch_name],
                        cwd=project_path,
                        check=True,
                        capture_output=True,
                    )

                    # Create module directory and files
                    module_path = os.path.join(project_path, "src", module_name)
                    os.makedirs(module_path, exist_ok=True)

                    # Create placeholder files
                    for file_name in module["files"]:
                        if file_name.endswith("/"):
                            # It's a directory
                            os.makedirs(
                                os.path.join(module_path, file_name), exist_ok=True
                            )
                        else:
                            file_path = os.path.join(module_path, file_name)
                            with open(file_path, "w") as f:
                                f.write(f"# {module['description']}\n")
                                f.write(
                                    f"# TODO: Implement {module_name} functionality\n"
                                )

                    # Commit initial module structure
                    subprocess.run(["git", "add", "."], cwd=project_path, check=True)
                    subprocess.run(
                        ["git", "commit", "-m", f"Add {module_name} module structure"],
                        cwd=project_path,
                        check=True,
                    )

                    # Switch back to develop branch
                    subprocess.run(
                        ["git", "checkout", "develop"], cwd=project_path, check=True
                    )

                    branch_results[module_name] = True

                except subprocess.CalledProcessError as e:
                    branch_results[module_name] = False

        except Exception as e:
            # If project path doesn't exist or git is not initialized
            for module in feature_modules:
                branch_results[module["name"]] = False

        return branch_results

    def _generate_development_plan(
        self, feature_modules: List[Dict[str, Any]], requirement: ProjectRequirement
    ) -> Dict[str, Any]:
        """Generate a development plan based on feature modules"""

        # Sort modules by priority and dependencies
        high_priority = [m for m in feature_modules if m["priority"] == "high"]
        medium_priority = [m for m in feature_modules if m["priority"] == "medium"]
        low_priority = [m for m in feature_modules if m["priority"] == "low"]

        # Create development phases
        phases = []

        # Phase 1: Core infrastructure
        core_modules = [m for m in high_priority if m["name"] in ["core", "utils"]]
        if core_modules:
            phases.append(
                {
                    "phase": 1,
                    "name": "Core Infrastructure",
                    "modules": core_modules,
                    "estimated_duration": "1-2 weeks",
                    "description": "Set up core functionality and utilities",
                }
            )

        # Phase 2: Main features
        main_modules = [m for m in high_priority if m["name"] not in ["core", "utils"]]
        if main_modules:
            phases.append(
                {
                    "phase": 2,
                    "name": "Core Features",
                    "modules": main_modules,
                    "estimated_duration": "2-4 weeks",
                    "description": "Implement main functionality",
                }
            )

        # Phase 3: Secondary features
        if medium_priority:
            phases.append(
                {
                    "phase": 3,
                    "name": "Secondary Features",
                    "modules": medium_priority,
                    "estimated_duration": "1-3 weeks",
                    "description": "Implement additional features and UI",
                }
            )

        # Phase 4: Testing and optimization
        test_modules = [m for m in feature_modules if m["name"] == "testing"]
        if test_modules or low_priority:
            phases.append(
                {
                    "phase": 4,
                    "name": "Testing & Optimization",
                    "modules": test_modules + low_priority,
                    "estimated_duration": "1-2 weeks",
                    "description": "Testing, optimization, and final touches",
                }
            )

        return {
            "total_modules": len(feature_modules),
            "development_phases": phases,
            "estimated_total_duration": self._estimate_duration(requirement),
            "recommended_team_size": self._recommend_team_size(requirement),
            "next_steps": [
                "Review and approve development plan",
                "Assign team members to modules",
                "Set up development environment",
                "Begin Phase 1 development",
            ],
        }
