import type { CSSObject } from '@ant-design/cssinjs';
import type { TypographyToken } from '.';
import type { GenerateStyle } from '../../theme/internal';
export declare const getTitleStyles: GenerateStyle<TypographyToken, CSSObject>;
export declare const getLinkStyles: GenerateStyle<TypographyToken, CSSObject>;
export declare const getResetStyles: GenerateStyle<TypographyToken, CSSObject>;
export declare const getEditableStyles: GenerateStyle<TypographyToken, CSSObject>;
export declare const getCopyableStyles: GenerateStyle<TypographyToken, CSSObject>;
export declare const getEllipsisStyles: () => CSSObject;
