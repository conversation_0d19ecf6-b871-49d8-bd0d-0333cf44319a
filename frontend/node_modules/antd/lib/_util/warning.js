"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.devUseWarning = exports.default = exports.WarningContext = void 0;
exports.noop = noop;
exports.resetWarned = resetWarned;
var React = _interopRequireWildcard(require("react"));
var _warning2 = _interopRequireWildcard(require("rc-util/lib/warning"));
function noop() {}
let deprecatedWarnList = null;
function resetWarned() {
  deprecatedWarnList = null;
  (0, _warning2.resetWarned)();
}
let _warning = noop;
if (process.env.NODE_ENV !== 'production') {
  _warning = (valid, component, message) => {
    (0, _warning2.default)(valid, `[antd: ${component}] ${message}`);
    // StrictMode will inject console which will not throw warning in React 17.
    if (process.env.NODE_ENV === 'test') {
      resetWarned();
    }
  };
}
const warning = _warning;
const WarningContext = exports.WarningContext = /*#__PURE__*/React.createContext({});
/**
 * This is a hook but we not named as `useWarning`
 * since this is only used in development.
 * We should always wrap this in `if (process.env.NODE_ENV !== 'production')` condition
 */
const devUseWarning = exports.devUseWarning = process.env.NODE_ENV !== 'production' ? component => {
  const {
    strict
  } = React.useContext(WarningContext);
  const typeWarning = (valid, type, message) => {
    if (!valid) {
      if (strict === false && type === 'deprecated') {
        const existWarning = deprecatedWarnList;
        if (!deprecatedWarnList) {
          deprecatedWarnList = {};
        }
        deprecatedWarnList[component] = deprecatedWarnList[component] || [];
        if (!deprecatedWarnList[component].includes(message || '')) {
          deprecatedWarnList[component].push(message || '');
        }
        // Warning for the first time
        if (!existWarning) {
          console.warn('[antd] There exists deprecated usage in your code:', deprecatedWarnList);
        }
      } else {
        process.env.NODE_ENV !== "production" ? warning(valid, component, message) : void 0;
      }
    }
  };
  typeWarning.deprecated = (valid, oldProp, newProp, message) => {
    typeWarning(valid, 'deprecated', `\`${oldProp}\` is deprecated. Please use \`${newProp}\` instead.${message ? ` ${message}` : ''}`);
  };
  return typeWarning;
} : () => {
  const noopWarning = () => {};
  noopWarning.deprecated = noop;
  return noopWarning;
};
var _default = exports.default = warning;