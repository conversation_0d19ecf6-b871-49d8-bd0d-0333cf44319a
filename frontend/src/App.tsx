import React, { useState, useEffect } from 'react';
import { Layout, Menu, Card, Spin, message, Button, Space, Typography } from 'antd';
import {
  RobotOutlined,
  FileTextOutlined,
  DashboardOutlined,
  SettingOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import RequirementForm from './components/RequirementForm';
import WorkflowResults from './components/WorkflowResults';
import AgentService from './services/agentService';
import { ProjectRequirement, WorkflowResult, Agent } from './types/agent';
import 'antd/dist/reset.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

function App() {
  const [selectedKey, setSelectedKey] = useState('submit');
  const [loading, setLoading] = useState(false);
  const [workflowResult, setWorkflowResult] = useState<WorkflowResult | null>(null);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentsLoading, setAgentsLoading] = useState(false);

  useEffect(() => {
    loadAgentsStatus();
  }, []);

  const loadAgentsStatus = async () => {
    setAgentsLoading(true);
    try {
      const response = await AgentService.getAgentsStatus();
      setAgents(response.agents);
    } catch (error) {
      message.error('Failed to load agents status');
    } finally {
      setAgentsLoading(false);
    }
  };

  const handleRequirementSubmit = async (requirement: ProjectRequirement) => {
    setLoading(true);
    try {
      const response = await AgentService.executeCompleteWorkflow(requirement);

      if (response.success) {
        setWorkflowResult(response.data);
        setSelectedKey('results');
        message.success('Project initialization completed successfully!');
      } else {
        message.error(response.message || 'Workflow execution failed');
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || 'Failed to execute workflow');
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'submit':
        return (
          <RequirementForm
            onSubmit={handleRequirementSubmit}
            loading={loading}
          />
        );
      case 'results':
        return workflowResult ? (
          <WorkflowResults result={workflowResult} />
        ) : (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Text type="secondary">No results available. Please submit a requirement first.</Text>
            </div>
          </Card>
        );
      case 'dashboard':
        return (
          <Card title="Agent Dashboard">
            <Spin spinning={agentsLoading}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ marginBottom: 16 }}>
                  <Button onClick={loadAgentsStatus} icon={<PlayCircleOutlined />}>
                    Refresh Status
                  </Button>
                </div>
                {agents.map(agent => (
                  <Card key={agent.id} size="small">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <Title level={5} style={{ margin: 0 }}>{agent.name}</Title>
                        <Text type="secondary">ID: {agent.id}</Text>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div>
                          <Text strong>Status: </Text>
                          <Text style={{
                            color: agent.status === 'idle' ? '#52c41a' :
                                   agent.status === 'busy' ? '#fa8c16' : '#ff4d4f'
                          }}>
                            {agent.status.toUpperCase()}
                          </Text>
                        </div>
                        <div>
                          <Text strong>Tasks: </Text>
                          <Text>{agent.current_tasks}</Text>
                        </div>
                      </div>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Text strong>Capabilities: </Text>
                      {agent.capabilities.map(cap => (
                        <span key={cap} style={{
                          background: '#f0f0f0',
                          padding: '2px 6px',
                          borderRadius: '4px',
                          marginRight: '4px',
                          fontSize: '12px'
                        }}>
                          {cap}
                        </span>
                      ))}
                    </div>
                  </Card>
                ))}
              </Space>
            </Spin>
          </Card>
        );
      default:
        return <div>Select a menu item</div>;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#001529', padding: '0 24px' }}>
        <div style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
          <RobotOutlined style={{ marginRight: 8 }} />
          DevOps Multi-Agent System
        </div>
      </Header>

      <Layout>
        <Sider width={250} style={{ background: '#fff' }}>
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            onClick={({ key }) => setSelectedKey(key)}
            style={{ height: '100%', borderRight: 0 }}
          >
            <Menu.Item key="submit" icon={<FileTextOutlined />}>
              Submit Requirement
            </Menu.Item>
            <Menu.Item key="results" icon={<SettingOutlined />}>
              View Results
            </Menu.Item>
            <Menu.Item key="dashboard" icon={<DashboardOutlined />}>
              Agent Dashboard
            </Menu.Item>
          </Menu>
        </Sider>

        <Layout style={{ padding: '24px' }}>
          <Content style={{ background: '#fff', padding: 24, margin: 0, minHeight: 280 }}>
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
}

export default App;
