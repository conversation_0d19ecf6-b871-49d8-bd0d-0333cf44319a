/**
 * Type definitions for agent system
 */

export enum RequirementType {
  COMPUTER_VISION = "computer_vision",
  MACHINE_LEARNING = "machine_learning", 
  DATA_PROCESSING = "data_processing",
  REAL_TIME_ANALYSIS = "real_time_analysis"
}

export enum FrameworkType {
  ALGORITHM_SDK = "algorithm_sdk",
  IMAGE_ANALYSIS = "image_analysis",
  VIDEO_STREAM = "video_stream"
}

export enum AgentStatus {
  IDLE = "idle",
  BUSY = "busy",
  ERROR = "error",
  OFFLINE = "offline"
}

export interface ProjectRequirement {
  title: string;
  description: string;
  requirement_type: RequirementType;
  technical_specs: Record<string, any>;
  expected_features: string[];
  performance_requirements: Record<string, any>;
  deadline?: string;
  priority: "low" | "medium" | "high" | "urgent";
}

export interface Agent {
  id: string;
  name: string;
  status: AgentStatus;
  capabilities: string[];
  current_tasks: number;
}

export interface Framework {
  type: FrameworkType;
  name: string;
  description: string;
  supported_requirements: RequirementType[];
  base_dependencies: string[];
}

export interface FeatureModule {
  name: string;
  description: string;
  priority: "low" | "medium" | "high";
  estimated_effort: string;
  dependencies: string[];
  files: string[];
}

export interface DevelopmentPhase {
  phase: number;
  name: string;
  modules: FeatureModule[];
  estimated_duration: string;
  description: string;
}

export interface DevelopmentPlan {
  total_modules: number;
  development_phases: DevelopmentPhase[];
  estimated_total_duration: string;
  recommended_team_size: number;
  next_steps: string[];
}

export interface AnalysisResult {
  status: string;
  selected_framework: string;
  framework_details: Framework;
  project_config: {
    project_name: string;
    framework_type: string;
    dependencies: string[];
    directory_structure: Record<string, any>;
    config_files: Record<string, string>;
    git_config: {
      main_branch: string;
      develop_branch: string;
      feature_prefix: string;
      release_prefix: string;
      hotfix_prefix: string;
    };
  };
  requirement_analysis: {
    complexity: "low" | "medium" | "high";
    estimated_duration: string;
    recommended_team_size: number;
  };
}

export interface InitializationResult {
  status: string;
  project_path: string;
  git_initialized: boolean;
  structure_created: boolean;
  configs_created: boolean;
  environment_initialized: boolean;
}

export interface DecompositionResult {
  status: string;
  feature_modules: FeatureModule[];
  branches_created: Record<string, boolean>;
  development_plan: DevelopmentPlan;
}

export interface WorkflowResult {
  analysis: AnalysisResult;
  initialization: InitializationResult;
  decomposition: DecompositionResult;
}

export interface AgentResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}
