/**
 * Agent service for API communication
 */
import axios from 'axios';
import {
  ProjectRequirement,
  Agent,
  Framework,
  AnalysisResult,
  InitializationResult,
  DecompositionResult,
  WorkflowResult,
  AgentResponse,
  RequirementType
} from '../types/agent';

const API_BASE_URL = 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export class AgentService {
  /**
   * Get status of all agents
   */
  static async getAgentsStatus(): Promise<{ agents: Agent[] }> {
    const response = await api.get('/agents/status');
    return response.data;
  }

  /**
   * Get capabilities of a specific agent
   */
  static async getAgentCapabilities(agentId: string): Promise<{
    agent_id: string;
    capabilities: string[];
    supported_frameworks: string[];
    supported_requirements: string[];
  }> {
    const response = await api.get(`/agents/${agentId}/capabilities`);
    return response.data;
  }

  /**
   * Submit a project requirement for analysis
   */
  static async submitRequirement(requirement: ProjectRequirement): Promise<AgentResponse<AnalysisResult>> {
    const response = await api.post('/agents/requirements/submit', requirement);
    return response.data;
  }

  /**
   * Initialize a project based on analysis results
   */
  static async initializeProject(projectConfig: any): Promise<AgentResponse<InitializationResult>> {
    const response = await api.post('/agents/projects/initialize', projectConfig);
    return response.data;
  }

  /**
   * Decompose features into modules and create branches
   */
  static async decomposeFeatures(requirementData: any): Promise<AgentResponse<DecompositionResult>> {
    const response = await api.post('/agents/features/decompose', requirementData);
    return response.data;
  }

  /**
   * Execute complete workflow: analyze -> initialize -> decompose
   */
  static async executeCompleteWorkflow(requirement: ProjectRequirement): Promise<AgentResponse<WorkflowResult>> {
    const response = await api.post('/agents/workflow/complete', requirement);
    return response.data;
  }

  /**
   * Get available frameworks
   */
  static async getAvailableFrameworks(): Promise<{ frameworks: Framework[] }> {
    const response = await api.get('/agents/frameworks');
    return response.data;
  }

  /**
   * Get supported requirement types
   */
  static async getRequirementTypes(): Promise<{
    requirement_types: Array<{
      value: string;
      name: string;
      description: string;
    }>;
  }> {
    const response = await api.get('/agents/requirements/types');
    return response.data;
  }
}

export default AgentService;
