/**
 * Workflow results display component
 */
import React from 'react';
import {
  Card,
  Steps,
  Descriptions,
  Tag,
  List,
  Progress,
  Row,
  Col,
  Alert,
  Collapse,
  Typography,
  Space,
  Timeline
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  BranchesOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { WorkflowResult, DevelopmentPhase, FeatureModule } from '../types/agent';

const { Step } = Steps;
const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;

interface WorkflowResultsProps {
  result: WorkflowResult;
}

const WorkflowResults: React.FC<WorkflowResultsProps> = ({ result }) => {
  const { analysis, initialization, decomposition } = result;

  const getStepStatus = (stepResult: any) => {
    if (!stepResult) return 'wait';
    return stepResult.status === 'success' ? 'finish' : 'error';
  };

  const getStepIcon = (stepResult: any) => {
    if (!stepResult) return <ClockCircleOutlined />;
    return stepResult.status === 'success' ? <CheckCircleOutlined /> : <CloseCircleOutlined />;
  };

  const renderAnalysisResults = () => (
    <Card title="Analysis Results" style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={12}>
          <Descriptions column={1} size="small">
            <Descriptions.Item label="Selected Framework">
              <Tag color="blue">{analysis.selected_framework}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Project Name">
              {analysis.project_config.project_name}
            </Descriptions.Item>
            <Descriptions.Item label="Complexity">
              <Tag color={
                analysis.requirement_analysis.complexity === 'high' ? 'red' :
                analysis.requirement_analysis.complexity === 'medium' ? 'orange' : 'green'
              }>
                {analysis.requirement_analysis.complexity.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Estimated Duration">
              {analysis.requirement_analysis.estimated_duration}
            </Descriptions.Item>
            <Descriptions.Item label="Recommended Team Size">
              <Space>
                <TeamOutlined />
                {analysis.requirement_analysis.recommended_team_size} developers
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Col>
        <Col span={12}>
          <Title level={5}>Framework Details</Title>
          <Paragraph>
            <Text strong>Name:</Text> {analysis.framework_details.name}
          </Paragraph>
          <Paragraph>
            <Text strong>Description:</Text> {analysis.framework_details.description}
          </Paragraph>
          <div>
            <Text strong>Dependencies:</Text>
            <div style={{ marginTop: 8 }}>
              {analysis.framework_details.base_dependencies.map(dep => (
                <Tag key={dep} style={{ margin: '2px' }}>{dep}</Tag>
              ))}
            </div>
          </div>
        </Col>
      </Row>
    </Card>
  );

  const renderInitializationResults = () => (
    <Card title="Project Initialization" style={{ marginBottom: 16 }}>
      <Alert
        message={initialization.status === 'success' ? 'Project initialized successfully' : 'Initialization failed'}
        type={initialization.status === 'success' ? 'success' : 'error'}
        style={{ marginBottom: 16 }}
      />
      
      <Row gutter={16}>
        <Col span={12}>
          <List
            size="small"
            dataSource={[
              { label: 'Git Repository', status: initialization.git_initialized },
              { label: 'Directory Structure', status: initialization.structure_created },
              { label: 'Configuration Files', status: initialization.configs_created },
              { label: 'Virtual Environment', status: initialization.environment_initialized }
            ]}
            renderItem={item => (
              <List.Item>
                <Space>
                  {item.status ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
                  {item.label}
                </Space>
              </List.Item>
            )}
          />
        </Col>
        <Col span={12}>
          <Descriptions column={1} size="small">
            <Descriptions.Item label="Project Path">
              <Text code>{initialization.project_path}</Text>
            </Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </Card>
  );

  const renderFeatureModule = (module: FeatureModule) => (
    <Card size="small" style={{ marginBottom: 8 }}>
      <Row>
        <Col span={18}>
          <Space direction="vertical" size="small">
            <div>
              <Text strong>{module.name}</Text>
              <Tag color={
                module.priority === 'high' ? 'red' :
                module.priority === 'medium' ? 'orange' : 'green'
              } style={{ marginLeft: 8 }}>
                {module.priority}
              </Tag>
            </div>
            <Text type="secondary">{module.description}</Text>
            <div>
              <Text strong>Dependencies:</Text> {module.dependencies.join(', ') || 'None'}
            </div>
          </Space>
        </Col>
        <Col span={6} style={{ textAlign: 'right' }}>
          <div>
            <Text strong>Effort:</Text>
            <br />
            <Text>{module.estimated_effort}</Text>
          </div>
        </Col>
      </Row>
    </Card>
  );

  const renderDevelopmentPlan = () => (
    <Card title="Development Plan" style={{ marginBottom: 16 }}>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                {decomposition.development_plan.total_modules}
              </Title>
              <Text>Total Modules</Text>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
                {decomposition.development_plan.estimated_total_duration}
              </Title>
              <Text>Estimated Duration</Text>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#fa8c16' }}>
                {decomposition.development_plan.recommended_team_size}
              </Title>
              <Text>Team Size</Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Timeline>
        {decomposition.development_plan.development_phases.map((phase: DevelopmentPhase) => (
          <Timeline.Item
            key={phase.phase}
            dot={<CodeOutlined style={{ fontSize: '16px' }} />}
          >
            <Card size="small">
              <Title level={5}>Phase {phase.phase}: {phase.name}</Title>
              <Paragraph>{phase.description}</Paragraph>
              <Text strong>Duration: </Text><Text>{phase.estimated_duration}</Text>
              <div style={{ marginTop: 8 }}>
                {phase.modules.map(module => renderFeatureModule(module))}
              </div>
            </Card>
          </Timeline.Item>
        ))}
      </Timeline>

      <Card size="small" style={{ marginTop: 16 }}>
        <Title level={5}>Next Steps</Title>
        <List
          size="small"
          dataSource={decomposition.development_plan.next_steps}
          renderItem={(step, index) => (
            <List.Item>
              <Text>{index + 1}. {step}</Text>
            </List.Item>
          )}
        />
      </Card>
    </Card>
  );

  const renderBranchStatus = () => (
    <Card title="Feature Branches" style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        {Object.entries(decomposition.branches_created).map(([branch, success]) => (
          <Col span={8} key={branch} style={{ marginBottom: 8 }}>
            <Card size="small">
              <Space>
                {success ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
                <div>
                  <Text strong>feature/{branch}</Text>
                  <br />
                  <Text type="secondary">{success ? 'Created' : 'Failed'}</Text>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto' }}>
      <Card title="Project Initialization Workflow Results" style={{ marginBottom: 16 }}>
        <Steps current={3} status="finish">
          <Step
            title="Requirement Analysis"
            status={getStepStatus(analysis)}
            icon={getStepIcon(analysis)}
          />
          <Step
            title="Project Initialization"
            status={getStepStatus(initialization)}
            icon={getStepIcon(initialization)}
          />
          <Step
            title="Feature Decomposition"
            status={getStepStatus(decomposition)}
            icon={getStepIcon(decomposition)}
          />
        </Steps>
      </Card>

      <Collapse defaultActiveKey={['1', '2', '3', '4']}>
        <Panel header="Analysis Results" key="1">
          {renderAnalysisResults()}
        </Panel>
        <Panel header="Project Initialization" key="2">
          {renderInitializationResults()}
        </Panel>
        <Panel header="Development Plan" key="3">
          {renderDevelopmentPlan()}
        </Panel>
        <Panel header="Feature Branches" key="4">
          {renderBranchStatus()}
        </Panel>
      </Collapse>
    </div>
  );
};

export default WorkflowResults;
