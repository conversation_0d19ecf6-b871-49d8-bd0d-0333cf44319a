/**
 * Requirement submission form component
 */
import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Space,
  Tag,
  Divider,
  Row,
  Col,
  DatePicker,
  message,
  Spin
} from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { ProjectRequirement, RequirementType } from '../types/agent';
import AgentService from '../services/agentService';

const { TextArea } = Input;
const { Option } = Select;

interface RequirementFormProps {
  onSubmit: (requirement: ProjectRequirement) => void;
  loading?: boolean;
}

const RequirementForm: React.FC<RequirementFormProps> = ({ onSubmit, loading = false }) => {
  const [form] = Form.useForm();
  const [features, setFeatures] = useState<string[]>(['']);
  const [techSpecs, setTechSpecs] = useState<Array<{ key: string; value: string }>>([{ key: '', value: '' }]);
  const [perfReqs, setPerfReqs] = useState<Array<{ key: string; value: string }>>([{ key: '', value: '' }]);
  const [requirementTypes, setRequirementTypes] = useState<Array<{ value: string; name: string; description: string }>>([]);
  const [loadingTypes, setLoadingTypes] = useState(false);

  useEffect(() => {
    loadRequirementTypes();
  }, []);

  const loadRequirementTypes = async () => {
    setLoadingTypes(true);
    try {
      const response = await AgentService.getRequirementTypes();
      setRequirementTypes(response.requirement_types);
    } catch (error) {
      message.error('Failed to load requirement types');
    } finally {
      setLoadingTypes(false);
    }
  };

  const addFeature = () => {
    setFeatures([...features, '']);
  };

  const removeFeature = (index: number) => {
    const newFeatures = features.filter((_, i) => i !== index);
    setFeatures(newFeatures);
  };

  const updateFeature = (index: number, value: string) => {
    const newFeatures = [...features];
    newFeatures[index] = value;
    setFeatures(newFeatures);
  };

  const addTechSpec = () => {
    setTechSpecs([...techSpecs, { key: '', value: '' }]);
  };

  const removeTechSpec = (index: number) => {
    const newSpecs = techSpecs.filter((_, i) => i !== index);
    setTechSpecs(newSpecs);
  };

  const updateTechSpec = (index: number, field: 'key' | 'value', value: string) => {
    const newSpecs = [...techSpecs];
    newSpecs[index][field] = value;
    setTechSpecs(newSpecs);
  };

  const addPerfReq = () => {
    setPerfReqs([...perfReqs, { key: '', value: '' }]);
  };

  const removePerfReq = (index: number) => {
    const newReqs = perfReqs.filter((_, i) => i !== index);
    setPerfReqs(newReqs);
  };

  const updatePerfReq = (index: number, field: 'key' | 'value', value: string) => {
    const newReqs = [...perfReqs];
    newReqs[index][field] = value;
    setPerfReqs(newReqs);
  };

  const handleSubmit = (values: any) => {
    // Convert arrays to objects
    const technicalSpecs: Record<string, any> = {};
    techSpecs.forEach(spec => {
      if (spec.key && spec.value) {
        technicalSpecs[spec.key] = spec.value;
      }
    });

    const performanceRequirements: Record<string, any> = {};
    perfReqs.forEach(req => {
      if (req.key && req.value) {
        performanceRequirements[req.key] = req.value;
      }
    });

    const requirement: ProjectRequirement = {
      title: values.title,
      description: values.description,
      requirement_type: values.requirement_type,
      technical_specs: technicalSpecs,
      expected_features: features.filter(f => f.trim() !== ''),
      performance_requirements: performanceRequirements,
      deadline: values.deadline?.format('YYYY-MM-DD'),
      priority: values.priority || 'medium'
    };

    onSubmit(requirement);
  };

  return (
    <Card title="Submit Project Requirement" style={{ maxWidth: 800, margin: '0 auto' }}>
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ priority: 'medium' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="Project Title"
                rules={[{ required: true, message: 'Please enter project title' }]}
              >
                <Input placeholder="Enter project title" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="requirement_type"
                label="Requirement Type"
                rules={[{ required: true, message: 'Please select requirement type' }]}
              >
                <Select placeholder="Select requirement type" loading={loadingTypes}>
                  {requirementTypes.map(type => (
                    <Option key={type.value} value={type.value}>
                      <div>
                        <div>{type.name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>{type.description}</div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Project Description"
            rules={[{ required: true, message: 'Please enter project description' }]}
          >
            <TextArea rows={4} placeholder="Describe your project requirements in detail" />
          </Form.Item>

          <Divider>Expected Features</Divider>
          <div style={{ marginBottom: 16 }}>
            {features.map((feature, index) => (
              <div key={index} style={{ display: 'flex', marginBottom: 8, alignItems: 'center' }}>
                <Input
                  placeholder="Enter expected feature"
                  value={feature}
                  onChange={(e) => updateFeature(index, e.target.value)}
                  style={{ marginRight: 8 }}
                />
                {features.length > 1 && (
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeFeature(index)}
                  />
                )}
              </div>
            ))}
            <Button type="dashed" onClick={addFeature} icon={<PlusOutlined />}>
              Add Feature
            </Button>
          </div>

          <Divider>Technical Specifications</Divider>
          <div style={{ marginBottom: 16 }}>
            {techSpecs.map((spec, index) => (
              <Row key={index} gutter={8} style={{ marginBottom: 8 }}>
                <Col span={10}>
                  <Input
                    placeholder="Specification name"
                    value={spec.key}
                    onChange={(e) => updateTechSpec(index, 'key', e.target.value)}
                  />
                </Col>
                <Col span={12}>
                  <Input
                    placeholder="Specification value"
                    value={spec.value}
                    onChange={(e) => updateTechSpec(index, 'value', e.target.value)}
                  />
                </Col>
                <Col span={2}>
                  {techSpecs.length > 1 && (
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeTechSpec(index)}
                    />
                  )}
                </Col>
              </Row>
            ))}
            <Button type="dashed" onClick={addTechSpec} icon={<PlusOutlined />}>
              Add Technical Specification
            </Button>
          </div>

          <Divider>Performance Requirements</Divider>
          <div style={{ marginBottom: 16 }}>
            {perfReqs.map((req, index) => (
              <Row key={index} gutter={8} style={{ marginBottom: 8 }}>
                <Col span={10}>
                  <Input
                    placeholder="Performance metric"
                    value={req.key}
                    onChange={(e) => updatePerfReq(index, 'key', e.target.value)}
                  />
                </Col>
                <Col span={12}>
                  <Input
                    placeholder="Required value"
                    value={req.value}
                    onChange={(e) => updatePerfReq(index, 'value', e.target.value)}
                  />
                </Col>
                <Col span={2}>
                  {perfReqs.length > 1 && (
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removePerfReq(index)}
                    />
                  )}
                </Col>
              </Row>
            ))}
            <Button type="dashed" onClick={addPerfReq} icon={<PlusOutlined />}>
              Add Performance Requirement
            </Button>
          </div>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="priority" label="Priority">
                <Select>
                  <Option value="low">Low</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="high">High</Option>
                  <Option value="urgent">Urgent</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="deadline" label="Deadline (Optional)">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Submit Requirement
              </Button>
              <Button onClick={() => form.resetFields()}>
                Reset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Spin>
    </Card>
  );
};

export default RequirementForm;
