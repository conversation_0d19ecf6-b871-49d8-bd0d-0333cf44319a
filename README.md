# DevOps Project Management System

这是一个用于研发团队进行研发项目管理的前后端分离项目。

## 项目结构

```
devops_prj/
├── backend/          # 后端微服务
├── frontend/         # 前端应用
├── docs/            # 项目文档
├── scripts/         # 部署和构建脚本
└── README.md        # 项目说明
```

## 技术栈

### 后端
- Python 3.11+
- FastAPI (微服务框架)
- SQLAlchemy (ORM)
- PostgreSQL (数据库)
- Redis (缓存)
- Celery (异步任务)

### 前端
- React 18
- TypeScript
- Vite (构建工具)
- Ant Design (UI组件库)
- React Query (状态管理)

## 开发环境

### 环境管理
- 使用 `uv` 管理Python环境和依赖
- 使用 Git Flow 进行分支管理

### 分支管理
- `main`: 生产环境分支
- `develop`: 开发环境分支
- `feature/*`: 功能开发分支
- `release/*`: 发布准备分支
- `hotfix/*`: 紧急修复分支

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd devops_prj
```

### 2. 后端环境设置
```bash
cd backend
uv venv
source .venv/bin/activate
uv pip install -r requirements.txt
```

### 3. 前端环境设置
```bash
cd frontend
npm install
npm run dev
```

## 贡献指南

1. 从 `develop` 分支创建功能分支
2. 完成开发后提交 Pull Request
3. 代码审查通过后合并到 `develop`
4. 定期从 `develop` 创建 `release` 分支进行发布

## 许可证

MIT License
